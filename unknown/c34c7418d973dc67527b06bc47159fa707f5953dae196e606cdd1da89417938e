@use '../../styles/variables' as *;

.ev-charging-station-page-container {
    .ev-charging-station-page-content {
        .ev-charging-station-header-image {
            position: relative;
            margin-bottom: 80px;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .header-text-overlay {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                display: flex;
                align-items: flex-end;
                justify-content: center;
                padding-bottom: 50px;

                .banner-title {
                    font-family: $font-prata;
                    font-weight: 400;
                    font-size: 64px;
                    text-align: center;
                    color: #FFFFFF;
                    background: linear-gradient(90deg, #8CFFE4 0%, #2499E2 62.5%);

                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
            }

            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.3);
                z-index: 1;
            }
        }

        .ev-charging-station-content {
            padding-left: 70px;
            padding-right: 70px;

            .ev-charging-station-title {
                margin-bottom: 30px;
                font-family: $font-prata;
                font-weight: 400;
                font-size: 64px;
                line-height: 70px;
                letter-spacing: 0%;
                text-align: center;
                background: linear-gradient(90deg, #8CFFE4 0%, #2499E2 62.5%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .ev-charging-station-description-container {
                display: flex;
                flex-direction: column;
                gap: 30px;
                margin-bottom: 70px;

                .ev-charging-station-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;
                }
            }
        }

        .ev-charging-station-why-us-container {
            margin-bottom: 50px;

            .ev-charging-station-why-us-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 30px;

                letter-spacing: 0%;
                vertical-align: middle;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 50px;
            }

            .ev-charging-station-why-us-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                vertical-align: middle;
                color: #FFFFFF;
                margin-bottom: 43px;
            }

            .ev-charging-station-why-us-list-container {
                display: flex;
                justify-content: center;
                padding-left: 44px;
                padding-right: 38px;
                border: 1px solid #2499E280;
                margin-bottom: 50px;

                .ev-charging-station-why-us-list {

                    .ev-charging-station-why-us-list-item {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 24px;

                        line-height: 50px;
                        letter-spacing: 0%;
                        vertical-align: middle;
                        color: $text-white;
                        display: flex;
                        align-items: start;

                    }

                    & li::before {
                        content: "•";
                        color: #FFFFFF;
                        font-size: 20px;
                        margin-right: 20px;

                    }

                }
            }


        }

        .our-expertise-container {
            margin-bottom: 80px;

            .our-expertise-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 34px;

                letter-spacing: 0%;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 50px;
            }

            .our-expertise-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                vertical-align: middle;
                color: #FFFFFF;
                margin-bottom: 31px;
            }

            .offering-item {
                .offering-item-title {
                    text-align: start;
                }
            }
        }

        .ev-charging-station-ocpp-container {
            .ev-charging-station-ocpp-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 30px;

                letter-spacing: 0%;
                vertical-align: middle;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 50px;
            }

            .ev-charging-station-ocpp-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                color: $text-white;
                margin-bottom: 50px;
            }

            .ev-charging-station-ocpp-list {
                margin-bottom: 80px;

                .ev-charging-station-ocpp-list-item {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 40px;
                    letter-spacing: 0%;
                    vertical-align: middle;
                    color: $text-white;


                }

                & li::before {
                    content: "•";
                    color: #FFFFFF;
                    font-size: 20px;
                    margin-right: 20px;
                }
            }

            .ev-charging-station-ocpp-image-container {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin-bottom: 13px;


                img {
                    background-color: $text-white;
                    padding: 48px 52px 47px 74px;
                    border-radius: 20px;
                    margin-bottom: 50px;
                }

                .image-caption {
                    font-family: Poppins !important;
                    font-weight: 400;
                    font-style: italic;
                    font-size: 18px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: center;
                    vertical-align: middle;
                    color: $text-white;
                    margin-bottom: 75px;
                }
            }

            .ev-charging-station-iot-integration-container {
                .iot-integration-title {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 30px;
                    letter-spacing: 0%;
                    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-bottom: 50px;
                }

                .iot-integration-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: $text-white;
                    margin-bottom: 50px;
                }

                .iot-integration-caption-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-style: italic;
                    font-size: 34px;

                    line-height: 60px;
                    letter-spacing: 0%;
                    text-align: center;
                    vertical-align: middle;
                    color: $text-white;
                }
            }

        }
    }
}