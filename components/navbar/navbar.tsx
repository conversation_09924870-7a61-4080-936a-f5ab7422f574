"use client";
import React, { useState, useRef, useEffect } from "react";
import ServiceNavDropdown from "@/components/serviceNavDropdown/serviceNavDropdown";
import IndustriesNavDropdown from "@/components/IndustriesNavDropdown/IndustriesNavDropdown";
import "./navbar.scss";
import {
  navItems,
  dropdownData,
  industriesDropdownData,
  industryRoutes,
  serviceRoutes,
} from "@/constant/index";
import {
  AppBar,
  Box,
  Toolbar,
  Button,
  Container,
  IconButton,
  Drawer,
  List,
  ListItem,
} from "@mui/material";
import Image from "next/image";
import { companyLogo } from "@/public/index";
import Link from "next/link";
import { usePathname } from "next/navigation";
import MenuIcon from "@mui/icons-material/Menu";

const Navbar: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [industriesAnchorEl, setIndustriesAnchorEl] =
    useState<null | HTMLElement>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [hoveredButton, setHoveredButton] = useState<string | null>(null);
  const pathname = usePathname();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navItemRef = useRef<HTMLDivElement>(null);
  const industriesDropdownRef = useRef<HTMLDivElement>(null);
  const industriesNavItemRef = useRef<HTMLDivElement>(null);

  const isServiceRoute = () => {
    return serviceRoutes.some((route) => pathname?.includes(route));
  };

  const isIndustryRoute = () => {
    return industryRoutes.some((route) => pathname?.includes(route));
  };

  const isNavItemActive = (label: string) => {
    if (label === "Services") {
      return Boolean(anchorEl) || isServiceRoute();
    }
    if (label === "Industries") {
      return Boolean(industriesAnchorEl) || isIndustryRoute();
    }
    return false;
  };

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    function handleMouseLeave(event: MouseEvent) {
      const servicesArea =
        dropdownRef.current?.contains(event.relatedTarget as Node) ||
        navItemRef.current?.contains(event.relatedTarget as Node);

      const industriesArea =
        industriesDropdownRef.current?.contains(event.relatedTarget as Node) ||
        industriesNavItemRef.current?.contains(event.relatedTarget as Node);

      // Only close if we're not moving to the dropdown or nav item
      if (!servicesArea && anchorEl) {
        timeoutId = setTimeout(() => setAnchorEl(null), 300); // Increased timeout
      }

      if (!industriesArea && industriesAnchorEl) {
        timeoutId = setTimeout(() => setIndustriesAnchorEl(null), 300); // Increased timeout
      }
    }

    document.addEventListener("mouseleave", handleMouseLeave, true);

    return () => {
      document.removeEventListener("mouseleave", handleMouseLeave, true);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [anchorEl, industriesAnchorEl]);

  // Add resize listener to handle drawer state
  useEffect(() => {
    function handleResize() {
      if (window.innerWidth > 1200 && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    }

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [mobileMenuOpen]);

  const handleMouseEnter = (
    event: React.MouseEvent<HTMLDivElement>,
    label: string
  ) => {
    const button = event.currentTarget.querySelector("button");
    if (label === "Services") {
      setAnchorEl(button);
      setIndustriesAnchorEl(null);
    } else if (label === "Industries") {
      setIndustriesAnchorEl(button);
      setAnchorEl(null);
    }
    if (button?.textContent === "Services") {
      setAnchorEl(event.currentTarget);
      setIndustriesAnchorEl(null);
    } else if (button?.textContent === "Industries") {
      setIndustriesAnchorEl(event.currentTarget);
      setAnchorEl(null);
    }
  };

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const isActive = (
    path: string | undefined,
    isIndustries: boolean = false
  ) => {
    if (!path) return false;
    const currentPath = pathname?.endsWith("/")
      ? pathname.slice(0, -1)
      : pathname;
    const normalizedPath = path.endsWith("/") ? path.slice(0, -1) : path;

    // For Industries nav item, check if current path is in industry routes
    if (isIndustries) {
      return industryRoutes.some(
        (route) =>
          (route.endsWith("/") ? route.slice(0, -1) : route) === currentPath
      );
    }

    return currentPath === normalizedPath;
  };

  const buttonStyles = {
    border: "1px solid transparent",
    color: "#D77D46",
    transition: "all 0.3s ease-in-out",
    "&:hover": {
      border: "1px solid #D77D46",
      background: "#D77D46",
      color: "#fff",
      transform: "translateY(-2px)",
    },
  };

  const getButtonStyles = (isButtonActive: boolean, label: string) => ({
    ...buttonStyles,
    ...(isButtonActive && !hoveredButton
      ? {
          border: "1px solid #D77D46",
          background: "#D77D46",
          color: "#fff",
        }
      : {}),
  });

  return (
    <AppBar
      className="appbar-container"
      position="static"
      sx={{ boxShadow: "none" }}
    >
      <Container maxWidth="xl" className="container-box">
        <Toolbar disableGutters className="toolbar-container">
          {/* Logo */}
          <Link href="/">
            <Image src={companyLogo} alt="Logo" width={160.14} height={54} />
          </Link>

          {/* Menu Icon for Mobile */}
          <Box sx={{ display: { xs: "block", lg: "none" } }}>
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={handleMobileMenuToggle}
            >
              <MenuIcon />
            </IconButton>
          </Box>

          {/* Navigation Links - Desktop */}
          <Box
            sx={{ display: { xs: "none", lg: "flex" }, gap: 2 }}
            onMouseLeave={() => setHoveredButton(null)}
          >
            {navItems.map((item) => (
              <Box
                key={item.label}
                ref={
                  item.label === "Services"
                    ? navItemRef
                    : item.label === "Industries"
                    ? industriesNavItemRef
                    : null
                }
                sx={{ position: "relative" }}
              >
                {item.label === "Services" || item.label === "Industries" ? (
                  <Box
                    onMouseEnter={(event) => {
                      handleMouseEnter(event, item.label);
                      setHoveredButton(item.label);
                    }}
                    sx={{
                      position: "relative",
                      "&::after": {
                        content: '""',
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        width: "100%",
                        height: "16px",
                        background: "transparent",
                      },
                    }}
                  >
                    <Button
                      className="nav-button"
                      sx={getButtonStyles(
                        isNavItemActive(item.label),
                        item.label
                      )}
                    >
                      {item.label}
                    </Button>
                  </Box>
                ) : (
                  <Link
                    href={item.path || "/"}
                    style={{ textDecoration: "none" }}
                  >
                    <Button
                      className="nav-button"
                      onMouseEnter={() => setHoveredButton(item.label)}
                      sx={getButtonStyles(isActive(item.path), item.label)}
                    >
                      {item.label}
                    </Button>
                  </Link>
                )}

                {item.label === "Services" && Boolean(anchorEl) && (
                  <Box
                    className="dropdown-menu-services"
                    ref={dropdownRef}
                    sx={{
                      position: "absolute",
                      top: "calc(100%)",
                      left: "30%",
                      transform: "translateX(-50%)",
                      zIndex: 1000,
                      opacity: 1,
                      transition: "opacity 0.3s ease-in-out",
                      paddingTop: "16px", // Add padding to maintain hover area
                    }}
                  >
                    <ServiceNavDropdown
                      data={dropdownData}
                      onClose={() => setAnchorEl(null)}
                    />
                  </Box>
                )}

                {item.label === "Industries" && Boolean(industriesAnchorEl) && (
                  <Box
                    className="dropdown-menu-industries"
                    ref={industriesDropdownRef}
                    sx={{
                      position: "absolute",
                      top: "calc(100%)",
                      left: "30%",
                      transform: "translateX(-70%)",
                      zIndex: 1000,
                      opacity: 1,
                      transition: "opacity 0.3s ease-in-out",
                      paddingTop: "16px", // Add padding to maintain hover area
                    }}
                  >
                    <IndustriesNavDropdown
                      data={industriesDropdownData}
                      onClose={() => setIndustriesAnchorEl(null)}
                    />
                  </Box>
                )}
              </Box>
            ))}
          </Box>

          {/* Mobile Menu Drawer */}
          <Drawer
            anchor="right"
            open={mobileMenuOpen}
            onClose={handleMobileMenuToggle}
            sx={{
              "& .MuiDrawer-paper": {
                width: "250px",
                background: "#021f2e",
                color: "white",
                padding: "20px",
              },
            }}
          >
            <List onMouseLeave={() => setHoveredButton(null)}>
              {navItems.map((item) => (
                <ListItem key={item.label} onClick={handleMobileMenuToggle}>
                  <Link
                    href={item.path || "/"}
                    style={{ textDecoration: "none", width: "100%" }}
                  >
                    <Button
                      className="nav-button"
                      fullWidth
                      onMouseEnter={() => setHoveredButton(item.label)}
                      sx={getButtonStyles(isActive(item.path), item.label)}
                    >
                      {item.label}
                    </Button>
                  </Link>
                </ListItem>
              ))}
            </List>
          </Drawer>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Navbar;
