import React from "react";
import "@/components/LetsTalk/LetsTalk.scss";
import {
  Box,
  Button,
  Grid,
  TextField,
  Typography,
  TextareaAutosize,
} from "@mui/material";

const LetsTalk: React.FC = () => {
  return (
    <Box className="lets-talk-container">
      <Typography className="lets-talk-title" variant="h4">
        Connect With Us!
      </Typography>

      <Typography className="lets-talk-description">
        Let us know how we can collaborate to create something valuable for your
        business.
      </Typography>

      <Box className="lets-talk-form">
        <Box className="lets-talk-form-header">
          <label>Name*</label>
          <TextField
            fullWidth
            variant="standard"
            InputProps={{
              disableUnderline: true,
              sx: {
                borderBottom: "1px dashed #D77D46",
                color: "#fff",
              },
            }}
            InputLabelProps={{ sx: { color: "#aaa" } }}
            sx={{ color: "#fff" }}
          />
        </Box>
        <Box className="lets-talk-form-header">
          <label>Email*</label>
          <TextField
            fullWidth
            variant="standard"
            InputProps={{
              disableUnderline: true,
              sx: {
                borderBottom: "1px dashed #D77D46",
                color: "#fff",
              },
            }}
            InputLabelProps={{ sx: { color: "#aaa" } }}
            sx={{ color: "#fff" }}
          />
        </Box>
        <Box className="lets-talk-form-header">
          <label>Phone</label>
          <TextField
            fullWidth
            variant="standard"
            InputProps={{
              disableUnderline: true,
              sx: {
                borderBottom: "1px dashed #D77D46",
                color: "#fff",
              },
            }}
            InputLabelProps={{ sx: { color: "#aaa" } }}
            sx={{ color: "#fff" }}
          />
        </Box>
      </Box>

      <Box className="lets-talk-textarea-container">
        <TextareaAutosize
          className="lets-talk-textarea"
          minRows={6}
          placeholder="Message Brief : Kindly provide small brief description so that we can connect you with right team ."
        />
        <Box className="lets-talk-buttons">
          <Button
            className="lets-talk-discard-button"
            variant="contained"
            // sx={{
            //   backgroundColor: "#e1e5e8",
            //   color: "#000",
            //   "&:hover": {
            // backgroundColor: "#cfd2d5",
            //   },
            //   px: 4,
            //   borderRadius: 2,
            //   fontWeight: 600,
            // }}
          >
            Discard
          </Button>
          <Button variant="contained" className="lets-talk-submit-button">
            Submit
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default LetsTalk;