import React from "react";
import "@/components/WhyUs/WhyUs.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { WhyUs_Img } from "@/public/index";

const WhyUs: React.FC = () => {
  return (
    <Box className="why-us-section-container">
      <Typography className="why-us-title" variant="h5">
        Why Us?
      </Typography>
      <Box className="why-us-content-wrapper">
        <Box className="why-us-content-left">
          <Box className="why-us-description-container">
            <Typography className="why-us-description">
              With a strong focus on innovation, efficiency, and compliance,
              Aadvik TekLabs would be a valuable trusted partner for companies
              striving to stay ahead in their technology roadmap and establish
              themselves as industry leaders. IWe bring deep technical expertise
              and a strong commitment to supporting your technology needs in
              developing intelligent, scalable, and future-ready solutions.
            </Typography>
            <Typography className="why-us-description-2">
              If you're looking for expert support in AI & Vision Technologies,
              Embedded Systems, Mechanical Engineering, or Cloud-Based Solutions
              and Platforms, Aadvik TekLabs is your ideal innovation partner.
            </Typography>
          </Box>
        </Box>
        <Box className="why-us-image-right">
          <Image
            src={WhyUs_Img}
            alt="Our Values Section Image"
            className="why-us-image"
          />
        </Box>
      </Box>
    </Box>
  );
};

export default WhyUs;
