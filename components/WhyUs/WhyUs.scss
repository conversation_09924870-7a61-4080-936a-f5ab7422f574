.why-us-section-container {
  margin: 80px 70px;
  display: flex;
  flex-direction: column;
  padding-bottom: 80px;
  border-bottom: 1px solid #d9d9d9;

  .why-us-title {
    font-family: Prata !important;
    font-weight: 400;
    font-size: 64px;
    letter-spacing: 0%;
    text-align: center;
    background: linear-gradient(90deg, #8cffe4 0%, #2499e2 62.5%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 60px;
  }

  .why-us-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;

    .why-us-content-left {
      display: flex;
      flex-direction: column;

      .why-us-description-container {
        .why-us-description,
        .why-us-description-2 {
          color: #ffffff;
          font-family: "Poppins", sans-serif;
          font-weight: 400;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
        }

        .why-us-description-2 {
          margin-top: 25px;
        }
      }
    }

    .why-us-image-right {
      display: flex;
      justify-content: center;
      align-items: center;

      .why-us-image {
        width: 100%;
        height: auto;
        max-width: 600px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-5px);
        }
      }
    }
  }
}

// Media Queries
@media (max-width: 1024px) {
  .why-us-section-container {
    margin: 60px 40px;

    .why-us-content-wrapper {
      gap: 40px;

      .why-us-content-left {
        .why-us-description-container {
          .why-us-description,
          .why-us-description-2 {
            font-size: 18px;
          }
        }
      }

      .why-us-image-right {
        .why-us-image {
          max-width: 500px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .why-us-section-container {
    margin: 40px 20px;
    padding-bottom: 60px;

    .why-us-title {
      font-size: 36px;
      margin-bottom: 40px;
    }

    .why-us-content-wrapper {
      grid-template-columns: 1fr;
      gap: 30px;

      .why-us-content-left {
        order: 1;

        .why-us-description-container {
          .why-us-description,
          .why-us-description-2 {
            font-size: 16px;
            line-height: 1.6;
          }

          .why-us-description-2 {
            margin-top: 20px;
          }
        }
      }

      .why-us-image-right {
        order: 2;

        .why-us-image {
          max-width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .why-us-section-container {
    margin: 30px 15px;
    padding-bottom: 40px;

    .why-us-title {
      font-size: 28px;
      margin-bottom: 30px;
    }

    .why-us-content-wrapper {
      gap: 25px;

      .why-us-content-left {
        .why-us-description-container {
          .why-us-description,
          .why-us-description-2 {
            font-size: 14px;
            line-height: 1.5;
          }

          .why-us-description-2 {
            margin-top: 15px;
          }
        }
      }
    }
  }
}
