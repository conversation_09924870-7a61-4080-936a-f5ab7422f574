import React from "react";
import { Box, Typography } from "@mui/material";
import "./ProductCategoryCard.scss";

interface ProductCategoryCardProps {
  title: string;
  items: string[];
}

const ProductCategoryCard: React.FC<ProductCategoryCardProps> = ({
  title,
  items,
}) => {
  return (
    <Box className="product-category-card">
      <Typography className="category-title">{title}</Typography>
      <Box className="category-items">
        {items.map((item, index) => (
          <Typography key={index} className="category-item">
            • {item}
          </Typography>
        ))}
      </Box>
    </Box>
  );
};

export default ProductCategoryCard;
