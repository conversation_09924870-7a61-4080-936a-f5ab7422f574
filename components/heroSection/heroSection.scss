.hero-section-container {
  position: relative;
  margin-bottom: 80px;

  .swiper-section-container {
    position: relative;
    width: 100%;
    height: 90vh;
    overflow: hidden;

    .swiper-container {
      width: 100%;
      height: 100%;

      .swiper-slide {
        opacity: 0;
        transition: opacity 2s ease-in-out;

        &-active {
          opacity: 1;
        }
      }

      .swiper-button-next,
      .swiper-button-prev {
        color: white;

        &::after {
          font-size: 24px;
        }
      }

      .swiper-pagination-bullet {
        background: white;
        opacity: 0.5;

        &-active {
          opacity: 1;
        }
      }
    }

    .slide-image-container {
      position: relative;
      width: 100%;
      height: 100%;

      .overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 30%;
        background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.5));
        z-index: 1;
      }
    }
  }

  .hero-section-content {
    position: absolute;
    bottom: 40px;
    right: 40px;
    z-index: 2;
    padding: 20px;

    .hero-section-text {
      font-family: <PERSON><PERSON><PERSON> !important;
      font-weight: 500;
      font-size: 74px;
      line-height: 100%;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
      color: #FFFFFF;
      font-style: normal;
    }
  }

  // media queries

  @media (max-width: 768px) {
    .hero-section-container {
      .swiper-section-container {
        height: 60vh;

        .swiper-container {
          .swiper-button-next,
          .swiper-button-prev {
            &::after {
              font-size: 20px;
            }
          }
        }
      }

      .hero-section-content {
        margin-top: 40px;
        padding: 0 20px;

        .hero-section-text {
          font-size: 18px;
          line-height: 28px;
        }
      }
    }
  }
}
