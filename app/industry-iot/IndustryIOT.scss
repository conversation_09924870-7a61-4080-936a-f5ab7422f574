@use "../../styles/variables" as *;

.IndustryIOT-page-container {
  .IndustryIOT-page-content {
    .IndustryIOT-header-image {
      position: relative;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 80px;

      img {
        width: 100%;
        height: auto;
      }

      .header-text-overlay {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 2;
        display: flex;
        flex-direction: column;
        padding: 50px;

        .main-title {
          font-family: Prata !important;
          font-weight: 400;
          font-size: 64px;
          line-height: 70px;
          letter-spacing: 0%;
          text-align: center;
          background: linear-gradient(90deg, #8cffe4 0%, #2499e2 62.5%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          position: absolute;
          left: 135px;
          top: 378px;
          transform: translateY(-50%);
        }

        .subtitle {
          font-family: Poppins !important;
          font-weight: 400;
          font-size: 30px;
          letter-spacing: 0%;
          text-align: center;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          position: absolute;
          bottom: 50px;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          width: 100%;
        }
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        z-index: 1;
      }
    }

    .IndustryIOT-content {
      padding-left: 70px;
      padding-right: 70px;
      margin-bottom: 95px;

      .IndustryIOT-title {
        margin-bottom: 33px;
        font-family: Poppins;
        font-weight: 400;
        font-size: 36px;

        line-height: 30px;
        letter-spacing: 0%;
        background: #8cffe4;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .IndustryIOT-description-container {
        display: flex;
        flex-direction: column;
        gap: 55px;
        margin-bottom: 80px;

        .IndustryIOT-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
        }
      }

      .IndustryIOT-image2-container {
        display: flex;
        justify-content: center;
        align-items: center;
        //margin-bottom: 50px;
        padding-bottom: 50px;

        // img {
        //     width: 100%;
        //     max-width: 100%;
        //     height: auto;
        // }
      }

      .IndustryIOT-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
        text-align: center;
        margin-top: 0%;
      }

      .IndustryIOT-description-2 {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
        margin-top: 80px;
        margin-bottom: 80px;
      }

      .our-offerings-title {
        font-family: Montserrat;
        font-weight: 500;
        font-size: 35px;
        line-height: 30px;
        letter-spacing: 0%;
        text-decoration: underline;
        text-decoration-style: solid;
        text-decoration-thickness: 0%;
        text-decoration-skip-ink: auto;
        color: #ffffff;
      }

      .our-iiot-solutions {
        .our-title {
          font-family: Montserrat;
          font-weight: 500;
          font-size: 35px;
          line-height: 30px;
          letter-spacing: 0%;
          text-decoration: underline;
          text-decoration-style: solid;
          text-decoration-thickness: 0%;
          text-decoration-skip-ink: auto;
          color: $text-white;
          margin-bottom: 50px;
        }

        .our-iiot-solutions-list {
          margin-bottom: 80px;
          display: flex;
          gap: 42.81px;

          .our-iiot-solutions-item {
            border: 1px solid #2499e280;
            width: 33.3333%;
            min-height: 351px;
            padding: 35px 22px 43px 28px;

            .our-iiot-solutions-item-image {
              display: flex;
              align-items: center;
              gap: 20px;
              justify-content: center;

              img {
                min-width: 78.07px;
                height: auto;
              }

              .our-iiot-solutions-item-title {
                font-family: Montserrat;
                font-weight: 500;
                font-size: 22px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: center;
                vertical-align: middle;
                background: #8cffe4;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 20px;
              }
            }

            .our-iiot-solutions-description {
              margin-top: 18px;
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;
              line-height: 30px;
              letter-spacing: 0%;
              vertical-align: middle;
              color: $text-white;
            }
          }
        }
        .our-iiot-description {
          font-family: Poppins !important;
          font-weight: 400;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          color: $text-white;
          margin-bottom: 80px;
        }
      }

      .why-do-we {
        margin-bottom: 50px;

        .why-do-we-title {
          font-family: Prata;
          font-weight: 400;
          font-size: 36px;
          line-height: 70px;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          background: linear-gradient(90deg, #8cffe4 0%, #2499e2 62.5%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 50px;
        }

        .why-do-we-description {
          display: flex;
          flex-direction: column;

          .why-do-we-description-text {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px !important;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: justify;
            vertical-align: middle;
            color: $text-white;
            margin-bottom: 50px;

            &:last-child {
              margin-bottom: 0;
            }

            span {
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;
              line-height: 30px;
              letter-spacing: 0%;
              vertical-align: middle;
              background: #2499e2;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }

        .why-do-we-list {
          ul {
            li {
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;
              line-height: 30px;
              letter-spacing: 0%;
              vertical-align: middle;
              color: $text-white;
            }

            & li::before {
              content: "•";
              color: $text-white;
              font-size: 20px;
              margin-right: 10px;
            }
          }
        }
      }

      .our-services {
        margin-bottom: 50px;

        .our-services-title {
          margin-bottom: 50px;
          font-family: Prata;
          font-weight: 400;
          font-size: clamp(36px, 5vw, 60px);
          line-height: 1.2;
          letter-spacing: 0%;
          text-align: center;
          background: linear-gradient(90deg, #8cffe4 0%, #2499e2 62.5%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .our-services-cards {
          display: flex;
          flex-direction: column;
          gap: 10.11px;
          margin-bottom: 50px;

          > div {
            display: flex;
            flex-wrap: wrap;
            gap: 10.11px;
            justify-content: center;

            .our-services-card {
              flex: 0 0 calc(33.33% - 7px);
              min-width: 280px;
              max-width: calc(33.33% - 7px);
              min-height: 308.99px;
              display: flex;
              justify-content: center;
              align-items: center;
              background: #093246;
              padding: clamp(20px, 3vw, 45px);

              @media (max-width: 1200px) {
                flex: 0 0 calc(50% - 5px);
                max-width: calc(50% - 5px);
              }

              @media (max-width: 768px) {
                flex: 0 0 100%;
                max-width: 100%;
              }

              .content {
                font-family: $font-port-lligat;
                font-weight: 400;
                font-size: clamp(36px, 4vw, 60px);
                line-height: 1.2;
                letter-spacing: 0%;
                text-align: center;
                vertical-align: middle;
                background: #2499e2;
                background-clip: text;
                -webkit-text-fill-color: transparent;
              }
            }
          }
        }

        .our-services-description {
          .our-services-description-text {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: justify;
            vertical-align: middle;
            color: $text-white;
            margin-bottom: 80px;
          }

          .our-services-description-text-2 {
            font-family: Poppins;
            font-weight: 400;
            font-style: italic;
            font-size: 34px;
            line-height: 60px;
            letter-spacing: 0%;
            text-align: center;
            vertical-align: middle;
            color: $text-white;
          }
        }
      }

      .ai-ml {
        margin-bottom: 76px;

        .ai-ml-title {
          font-family: Poppins;
          font-weight: 300;
          font-size: 34px;

          letter-spacing: 0%;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 54px;
        }

        .ai-ml-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
          margin-bottom: 40px;
        }

        .ai-ml-tech-stack {
          display: flex;
          gap: 139px;
          margin-left: 20px;

          ul {
            li {
              font-family: Poppins;
              font-weight: 400;
              font-size: 25px;

              line-height: 40px;
              letter-spacing: 0%;
              color: #ffffff;
            }
          }
        }
      }
    }

    .our-offerings {
      // padding-left: 91px;
      // padding-right: 110px;
      margin-bottom: 54px;

      .our-offerings-title {
        font-family: Poppins;
        font-weight: 400;
        font-size: 34px;

        letter-spacing: 0%;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 41px;
      }

      .our-offerings-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 34px;
      }

      .our-offerings-list {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .offering-item-wrapper {
          display: flex;
          gap: 15px;
        }

        .offering-item {
          padding: 38.09px 27.41px 39.58px 34px;
          width: 100%;
          min-height: 320px;
          border: 1px solid #2499e280;
          color: #ffffff;

          .offering-item-title {
            font-family: Poppins;
            font-weight: 400;
            font-size: 22px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: center;
            background: #8cffe4;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 30.48px;
          }
        }
      }
    }

    .success-stories {
      margin-bottom: 98px;

      .success-stories-description {
        font-family: Poppins;
        font-weight: 500;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 30px;
      }

      .bullets-point {
        li {
          display: flex;
          flex-direction: row !important;
          align-items: start;
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #ffffff;
          display: flex;
          flex-direction: column;
          gap: 30px;
        }

        & li::before {
          content: "•";
          color: #ffffff;
          font-size: 20px;
          // margin-right: 10px;
        }
      }
    }

    // .case-study {
    //     padding-left: 93px;
    //     padding-right: 103px;
    //     margin-bottom: 301px;

    //     .case-study-title {
    //         font-family: Poppins;
    //         font-weight: 400;
    //         font-size: 34px;

    //         letter-spacing: 0%;
    //         text-align: center;
    //         background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
    //         background-clip: text;
    //         -webkit-text-fill-color: transparent;
    //         margin-bottom: 61px;
    //     }

    //     .case-study-images {
    //         display: flex;
    //         justify-content: center;
    //         align-items: center;
    //         gap: 59px;
    //         margin-bottom: 77.17px;

    //         img {
    //             width: 100%;
    //             max-width: 270px;
    //             min-height: 200px;
    //             border-radius: 10px;
    //         }

    //         .case-study-image-item {
    //             display: flex;
    //             flex-direction: column;
    //             align-items: center;
    //             gap: 44px;

    //             .image-title {
    //                 font-family: Montserrat;
    //                 font-weight: 400;
    //                 font-size: 24px;

    //                 line-height: 30px;
    //                 letter-spacing: 0%;
    //                 vertical-align: bottom;
    //                 color: #FFFFFF;
    //             }
    //         }
    //     }

    //     .case-study-description {
    //         .case-study-text {
    //             font-family: Poppins;
    //             font-weight: 400;
    //             font-style: italic;
    //             font-size: 16px;

    //             line-height: 30px;
    //             letter-spacing: 0%;
    //             color: #FFFFFF;
    //         }
    //     }
    // }

    .case-study {
      margin-top: 160px;
      // padding-left: 93px;
      // padding-right: 103px;
      margin-bottom: 301px;

      .case-study-title {
        font-family: Poppins;
        font-weight: 400;
        font-size: 34px;

        letter-spacing: 0%;
        text-align: center;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 61px;
      }

      .case-study-images {
        display: flex;
        justify-content: center;
        align-items: center;
        //gap: 59px;
        margin-bottom: 77.17px;
        width: 940px;
        height: 356px;

        img {
          width: 100%;
          max-width: 270px;
          min-height: 200px;
          border-radius: 10px;
          transition: all 0.3s ease-in-out;
        }

        .case-study-image-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 44px;
          transition: all 0.3s ease-in-out;
          cursor: pointer;
          padding: 10px;
          border-radius: 12px;

          &:hover {
            transform: translateY(-10px);
            background: rgba(140, 255, 228, 0.05);

            img {
              transform: scale(1.05);
              box-shadow: 0 10px 20px rgba(140, 255, 228, 0.2);
            }

            .image-title {
              background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          img {
            transition: all 0.3s ease-in-out;
          }

          .image-title {
            font-family: Montserrat;
            font-weight: 400;
            font-size: 24px;

            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: bottom;
            color: #ffffff;
          }
        }
      }

      .case-study-description {
        .case-study-text {
          font-family: Poppins;
          font-weight: 400;
          font-style: italic;
          font-size: 16px;

          line-height: 30px;
          letter-spacing: 0%;
          color: #ffffff;
        }
      }
    }
  }
}