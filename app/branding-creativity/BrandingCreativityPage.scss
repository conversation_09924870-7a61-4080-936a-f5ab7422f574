.branding-page-container {
    .branding-page-content {
        .branding-header-image {
            width: 100%;
            max-width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 60px;

            img {
                width: 100%;
                max-width: 100%;
                height: auto;
            }
        }

        .branding-content {
            padding-left: 95px;
            padding-right: 95px;
            margin-bottom: 95px;

            .branding-title {
                margin-bottom: 33px;
                font-family: Poppins;
                font-weight: 400;
                font-size: 36px;

                line-height: 30px;
                letter-spacing: 0%;
                background: #8CFFE4;
                background-clip: text;
                -webkit-text-fill-color: transparent;

            }

            .branding-description-container {
                display: flex;
                flex-direction: column;
                gap: 55px;
                margin-bottom: 118px;

                .branding-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;

                }
            }

            .what-we-serve-container {
                margin-bottom: 76px;

                .branding-title {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 34px;

                    letter-spacing: 0%;
                    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    // margin-bottom: 61px;
                }

                .what-we-serve-list {
                    display: flex;
                    flex-direction: column;
                    gap: 20px;

                    .what-we-serve-item {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 24px;

                        line-height: 28px;
                        letter-spacing: 0%;
                        text-align: justify;
                        vertical-align: middle;
                        color: #FFFFFF;
                        display: flex;
                        align-items: start;

                        &::before {
                            content: "•";
                            position: absolute;
                            left: 0;
                            color: #FFFFFF;
                            font-size: 20px;
                        }
                    }



                }
            }



        }


        .product-design-image {
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;


            img {
                margin-bottom: 31px;
            }

            .image-title {
                font-family: Poppins;
                font-weight: 300;
                font-size: 34px;
                // 
                letter-spacing: 0%;
                text-align: center;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 67px;
            }
        }




        .case-study {
            margin-top: 160px;
            // padding-left: 93px;
            // padding-right: 103px;
            margin-bottom: 301px;

            .case-study-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 34px;

                letter-spacing: 0%;
                text-align: center;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 61px;
            }

            .case-study-images {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 59px;
                margin-bottom: 77.17px;

                img {
                    width: 100%;
                    max-width: 270px;
                    min-height: 200px;
                    border-radius: 10px;
                    transition: all 0.3s ease-in-out;
                }

                .case-study-image-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 44px;
                    transition: all 0.3s ease-in-out;
                    cursor: pointer;
                    padding: 10px;
                    border-radius: 12px;

                    &:hover {
                        transform: translateY(-10px);
                        background: rgba(140, 255, 228, 0.05);

                        img {
                            transform: scale(1.05);
                            box-shadow: 0 10px 20px rgba(140, 255, 228, 0.2);
                        }

                        .image-title {
                            background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                            background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }
                    }

                    img {
                        transition: all 0.3s ease-in-out;
                    }

                    .image-title {
                        font-family: Montserrat;
                        font-weight: 400;
                        font-size: 24px;

                        line-height: 30px;
                        letter-spacing: 0%;
                        vertical-align: bottom;
                        color: #FFFFFF;
                    }
                }
            }

            .case-study-description {
                .case-study-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-style: italic;
                    font-size: 16px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    color: #FFFFFF;
                }
            }
        }
    }
}