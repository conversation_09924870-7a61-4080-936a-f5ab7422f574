"use client";
import "./ConsumerElectronics.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { ConsumerElectronicsImg1, ConsumerElectronicsImg2 } from "@/public";
import ProductCategoryCard from "@/components/ProductCategoryCard/ProductCategoryCard";
import { consumerElectronicsData } from "@/constant/index";
import { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";

const ConsumerElectronicsPage = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-in-out",
    });
  }, []);

  return (
    <Box className="consumer-electronics-page-container">
      <Box className="consumer-electronics-page-content">
        <Box
          className="consumer-electronics-header-section"
          data-aos="fade-down"
          data-aos-duration="1200"
        >
          <Box className="banner-text-overlay">
            <Typography className="banner-title">
              {consumerElectronicsData.header.title}
            </Typography>
          </Box>
        </Box>

        {/* Content and Image Section */}
        <Box
          className="content-image-section"
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <Box
            className="content-text"
            data-aos="fade-right"
            data-aos-duration="1200"
            data-aos-delay="200"
          >
            {consumerElectronicsData.contentDescriptions.map((description, index) => (
              <Typography key={index} className="content-description">
                {description}
              </Typography>
            ))}
          </Box>
          <Box
            className="content-image"
            data-aos="fade-left"
            data-aos-duration="1200"
            data-aos-delay="400"
          >
            <Image
              src={ConsumerElectronicsImg1}
              alt="Consumer Electronics Appliances"
              className="appliances-image"
            />
          </Box>
        </Box>

        {/* Support and Delivery Section */}
        <Box
          className="support-delivery-section"
          data-aos="fade-up"
          data-aos-duration="1000"
          data-aos-delay="200"
        >
          <Box className="support-delivery-content">
            <Box
              className="support-column"
              data-aos="fade-right"
              data-aos-duration="1200"
              data-aos-delay="400"
            >
              <Typography className="section-title">
                {consumerElectronicsData.supportSection.title}
              </Typography>
              <Box className="bullet-list">
                {consumerElectronicsData.supportSection.services.map((service, index) => (
                  <Typography key={index} className="bullet-item">
                    • {service}
                  </Typography>
                ))}
              </Box>
            </Box>

            <Box
              className="delivery-column"
              data-aos="fade-left"
              data-aos-duration="1200"
              data-aos-delay="600"
            >
              <Typography className="section-title">
                {consumerElectronicsData.deliverySection.title}
              </Typography>
              <Box className="bullet-list">
                {consumerElectronicsData.deliverySection.services.map((service, index) => (
                  <Typography key={index} className="bullet-item">
                    • {service}
                  </Typography>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>

        {/* we ensure */}
        <Box
          className="we-ensure-section"
          data-aos="fade-up"
          data-aos-duration="1000"
          data-aos-delay="200"
        >
          <Typography
            className="we-ensure-title"
            data-aos="fade-down"
            data-aos-duration="1200"
            data-aos-delay="400"
          >
            {consumerElectronicsData.weEnsureSection.title}
          </Typography>
          <Typography
            className="we-ensure-description"
            data-aos="fade-up"
            data-aos-duration="1200"
            data-aos-delay="600"
          >
            {consumerElectronicsData.weEnsureSection.description}
          </Typography>
        </Box>

        {/* Key Product Categories We Support */}
        <Box
          className="product-categories-section"
          data-aos="fade-up"
          data-aos-duration="1000"
          data-aos-delay="200"
        >
          <Typography
            className="categories-main-title"
            data-aos="fade-down"
            data-aos-duration="1200"
            data-aos-delay="400"
          >
            {consumerElectronicsData.productCategoriesSection.title}
          </Typography>
          <Box
            className="categories-grid"
            data-aos="fade-up"
            data-aos-duration="1200"
            data-aos-delay="600"
          >
            {consumerElectronicsData.productCategoriesSection.categories.map((category, index) => (
              <ProductCategoryCard
                key={index}
                title={category.title}
                items={category.items}
              />
            ))}
          </Box>
        </Box>

        {/* we build next gen section */}
        <Box
          className="we-build-next-gen-section"
          data-aos="fade-up"
          data-aos-duration="1000"
          data-aos-delay="200"
        >
          <Box
            className="banner-text-overlay"
            data-aos="zoom-in"
            data-aos-duration="1200"
            data-aos-delay="400"
          >
            <Typography className="banner-title">
              {consumerElectronicsData.nextGenSection.title}
            </Typography>
          </Box>
        </Box>

        {/* icons group box */}
        <Box
          className="icons-group-box"
          // data-aos="fade-up"
          // data-aos-duration="1000"
          // data-aos-delay="200"
        >
          <Image
            src={ConsumerElectronicsImg2}
            alt="Consumer Electronics"            
          />
        </Box>
      </Box>
    </Box>
  );
};

export default ConsumerElectronicsPage;
