@use '../../styles/variables' as *;


.industrial-machinery-page-container {
    .industrial-machinery-page-content {
        .industrial-machinery-header-image {
            margin-bottom: 80px;

            img {
                width: 100%;
                height: 90vh;
                object-fit: cover;
            }
        }

        .industrial-machinery-content {
            padding: 0px 70px;

            .industrial-machinery-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                color: $text-white;
                margin-bottom: 80px;
            }

            .optimizing-performance-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 30px;
                text-align: center;
                letter-spacing: 0%;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 50px;
            }

            .optimizing-performance-content {
                display: flex;
                flex-direction: column;
                gap: 50px;
                margin-bottom: 80px;

                .optimizing-performance-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: $text-white;
                }
            }

            .key-offerings {
                .key-offerings-title {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 30px;
                    letter-spacing: 0%;
                    text-align: center;
                    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-bottom: 50px;
                }

                .key-offerings-content {
                    // margin-bottom: 80px;
                }

                .key-offerings-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: $text-white;
                    margin-bottom: 70px;
                }

                .product-engineering-services {
                    margin-bottom: 80px;

                    .product-engineering-services-title {
                        font-family: Montserrat;
                        font-weight: 500;
                        font-size: 35px;
                        line-height: 30px;
                        letter-spacing: 0%;
                        text-decoration: underline;
                        text-decoration-style: solid;
                        text-decoration-offset: 0%;
                        text-decoration-thickness: 0%;
                        text-decoration-skip-ink: auto;
                        color: $text-white;
                        margin-bottom: 50px;
                    }

                    .product-engineering-services-text {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 20px;
                        line-height: 30px;
                        letter-spacing: 0%;
                        text-align: justify;
                        vertical-align: middle;
                        color: $text-white;
                        margin-bottom: 50px;
                    }

                    .product-engineering-services-list {
                        .product-engineering-services-item {
                            font-family: Poppins;
                            font-weight: 400;
                            font-size: 20px;
                            line-height: 50px;
                            letter-spacing: 0%;
                            text-align: justify;
                            color: $text-white;
                        }

                        .product-engineering-services-item::before {
                            content: "•";
                            color: $text-white;
                            margin-right: 10px;
                        }


                    }
                }

                .technical-expertise {
                    .technical-expertise-title {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 30px;
                        letter-spacing: 0%;
                        text-align: center;
                        background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                        background-clip: text;
                        -webkit-text-fill-color: transparent;
                        margin-bottom: 50px;
                    }

                    .technical-expertise-content {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 25px;
                        justify-content: space-between;
                        margin-bottom: 50px;

                        .technical-expertise-item {
                            width: calc(33.33% - 17px); // Account for the gap between items
                            min-width: 417px;
                            margin-bottom: 25px;
                            box-shadow: 4px 4px 4px 0px #2499E2;
                            padding: 30px 28px 0px 9px;
                            border-radius: 20px;

                            .technical-expertise-item-title {
                                font-family: Poppins;
                                font-weight: 600;
                                font-size: 20px;
                                line-height: 30px;
                                letter-spacing: 0%;
                                text-align: center;
                                background: #2499E2;
                                background-clip: text;
                                -webkit-text-fill-color: transparent;
                            }

                            .technical-expertise-item-list-item {
                                font-family: Poppins;
                                font-weight: 400;
                                font-size: 20px;
                                line-height: 34px;
                                letter-spacing: 0%;
                                color: $text-white;
                                display: flex;
                                align-items: start;
                            }

                            .technical-expertise-item-list-item::before {
                                content: "•";
                                color: $text-white;
                                margin-right: 10px;
                            }
                        }
                    }

                    .technical-expertise-description {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 20px;

                        line-height: 30px;
                        letter-spacing: 0%;
                        text-align: justify;
                        vertical-align: middle;
                        color: $text-white;
                    }

                    .technical-expertise-list {
                        margin-bottom: 80px;

                        .technical-expertise-list-item {
                            font-family: Poppins;
                            font-weight: 400;
                            font-size: 20px;

                            line-height: 40px;
                            letter-spacing: 0%;
                            vertical-align: middle;
                            color: $text-white;
                        }

                        .technical-expertise-list-item::before {
                            content: "•";
                            color: $text-white;
                            margin-right: 10px;
                        }
                    }

                    .technical-expertise-caption-text {
                        font-family: Poppins;
                        font-weight: 400;
                        font-style: italic;
                        font-size: 34px;

                        line-height: 60px;
                        letter-spacing: 0%;
                        text-align: center;
                        vertical-align: middle;
                        color: $text-white;
                    }

                }
            }
        }
    }
}